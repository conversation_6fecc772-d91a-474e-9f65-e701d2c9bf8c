import React from 'react';
import { MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import KpiGrid from '@/components/dashboard/KpiGrid';

const DashboardMinimal = () => {
  const navigate = useNavigate();

  console.log('DashboardMinimal component rendering');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header simples */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Ícone do DataHero */}
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            
            {/* Indicadores de status */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
            </div>
          </div>
          
          {/* Botão para ir às perguntas */}
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            Fazer Perguntas
          </Button>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <h1 className="text-2xl font-bold mb-6">Dashboard - Teste Mínimo</h1>
          
          {/* Grid de KPIs */}
          <KpiGrid />
        </div>
      </div>
    </div>
  );
};

export default DashboardMinimal;
